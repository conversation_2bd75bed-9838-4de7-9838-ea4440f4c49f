import "server-only";

import { createTRPCOptionsProxy } from "@trpc/tanstack-react-query";
import { headers } from "next/headers";
import { cache } from "react";
import { createTRPCContext } from "./init";
import { makeQueryClient } from "./query-client";
import { appRouter } from "./routers/_app";

// IMPORTANT: Create a stable getter for the query client that
//            will return the same client during the same request.
export const getQueryClient = cache(makeQueryClient);

// Create a wrapper function that gets headers and passes them to createTRPCContext
const createServerContext = cache(async () => {
  const headersList = await headers();
  // Create a mock Request object for server-side usage
  const mockRequest = new Request("http://localhost", {
    headers: headersList,
  });
  return createTRPCContext({
    req: mockRequest,
    resHeaders: new Headers(),
  });
});

export const trpc = createTRPCOptionsProxy({
  ctx: createServerContext,
  router: appRouter,
  queryClient: getQueryClient,
});

export const caller = appRouter.createCaller(createServerContext);
