import type { NextPage } from "next";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { auth } from "@/lib/auth";
import { HomeView } from "@/modules/home/<USER>/views/home-view";

const PodcastsCreatePage: NextPage = async () => {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session) {
    redirect("/sign-in");
  }

  return <HomeView />;
};

export default PodcastsCreatePage;
