"use client";

import { HomeIcon, Podcast, SettingsIcon, StarIcon } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import type { FC } from "react";
import { Separator } from "@/components/ui/separator";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { DashboardUserButton } from "./dashboard-user-button";

const BASE_PATH = "/dashboard";

const firstSection = [
  {
    icon: HomeIcon,
    label: "Home",
    href: `${BASE_PATH}`,
  },
  {
    icon: Podcast,
    label: "Podcasts",
    href: `${BASE_PATH}/podcasts`,
  },
];

const secondSection = [
  {
    icon: StarIcon,
    label: "Upgrade",
    href: `${BASE_PATH}/upgrade`,
  },
  {
    icon: SettingsIcon,
    label: "Settings",
    href: `${BASE_PATH}/settings`,
  },
];

export const DashboardSidebar: FC = () => {
  const pathname = usePathname();

  return (
    <Sidebar>
      <SidebarHeader className="text-sidebar-accent-foreground">
        <Link
          href="/"
          className="flex items-center gap-2 px-2 pt-2 uppercase text-xs"
        >
          Podium&trade;
        </Link>
      </SidebarHeader>
      <div>
        <Separator className="opacity-10 text-[#5d6b68]" />
      </div>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {firstSection.map((item) => (
                <SidebarMenuItem key={item.label}>
                  <SidebarMenuButton
                    asChild
                    className={cn("h-10", {
                      "bg-sidebar-accent text-sidebar-accent-foreground":
                        pathname === item.href,
                    })}
                  >
                    <Link href={item.href}>
                      <item.icon className="size-3" />
                      <span className="text-sm font-medium tracking-tight">
                        {item.label}
                      </span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        <div>
          <Separator className="opacity-10 text-[#5d6b68]" />
        </div>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {secondSection.map((item) => (
                <SidebarMenuItem key={item.label}>
                  <SidebarMenuButton
                    asChild
                    className={cn("h-10", {
                      "bg-sidebar-accent text-sidebar-accent-foreground":
                        pathname === item.href,
                    })}
                  >
                    <Link href={item.href}>
                      <item.icon className="size-3" />
                      <span className="text-sm font-medium tracking-tight">
                        {item.label}
                      </span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <DashboardUserButton />
      </SidebarFooter>
    </Sidebar>
  );
};
